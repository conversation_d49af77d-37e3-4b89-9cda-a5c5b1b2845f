//
//  LotteryConfigFormData.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import Foundation

/**
 * 抽奖配置表单数据模型
 * 用于管理抽奖道具配置的表单数据和验证逻辑
 */
class LotteryConfigFormData: ObservableObject {
    
    // MARK: - 基本配置
    
    /// 道具类型
    @Published var toolType: LotteryConfig.ToolType = .wheel
    
    /// 道具数量（盲盒和刮刮卡可调整，大转盘4-12个分区）
    @Published var itemCount: Int = 8
    
    /// 每次抽奖消耗积分
    @Published var costPerPlay: Int = 10
    
    // MARK: - 道具项目配置
    
    /// 道具项目数据
    @Published var items: [LotteryConfigItemData] = []
    
    // MARK: - 验证状态
    
    /// 表单验证错误
    @Published var validationErrors: [String] = []
    
    /// 是否正在保存
    @Published var isSaving: Bool = false
    
    // MARK: - 计算属性
    
    /**
     * 检查表单是否有效
     */
    var isValid: Bool {
        validate()
        return validationErrors.isEmpty
    }
    
    /**
     * 获取最小道具数量
     */
    var minItemCount: Int {
        return toolType.minItemCount
    }
    
    /**
     * 获取最大道具数量
     */
    var maxItemCount: Int {
        return toolType.maxItemCount
    }
    
    /**
     * 获取本地化的项目标题前缀
     */
    var localizedItemTitlePrefix: String {
        switch toolType {
        case .wheel:
            return "分区"
        case .blindbox:
            return "盲盒"
        case .scratchcard:
            return "刮刮卡"
        }
    }
    
    // MARK: - 初始化
    
    init() {
        setupDefaultItems()
    }
    
    /**
     * 使用现有配置初始化
     */
    init(from config: LotteryConfig) {
        self.toolType = config.lotteryToolType
        self.itemCount = Int(config.itemCount)
        self.costPerPlay = Int(config.costPerPlay)
        
        // 加载现有项目数据
        loadItemsFromConfig(config)
    }
    
    // MARK: - 数据管理
    
    /**
     * 设置道具类型
     */
    func setToolType(_ type: LotteryConfig.ToolType) {
        self.toolType = type
        
        // 重置道具数量为默认值
        self.itemCount = type.defaultItemCount
        
        // 重新设置道具项目
        setupDefaultItems()
    }
    
    /**
     * 更新道具数量
     */
    func updateItemCount(_ newCount: Int) {
        let clampedCount = max(minItemCount, min(maxItemCount, newCount))
        
        if clampedCount != itemCount {
            itemCount = clampedCount
            setupDefaultItems()
        }
    }
    
    /**
     * 更新项目奖品名称
     */
    func updateItemPrizeName(at index: Int, name: String) {
        guard index >= 0 && index < items.count else { return }
        items[index].prizeName = name
    }
    
    /**
     * 设置默认项目
     */
    private func setupDefaultItems() {
        items.removeAll()
        
        for i in 0..<itemCount {
            let item = LotteryConfigItemData(
                index: i,
                prizeName: "",
                toolType: toolType
            )
            items.append(item)
        }
    }
    
    /**
     * 从现有配置加载数据
     */
    private func loadItemsFromConfig(_ config: LotteryConfig) {
        items.removeAll()
        
        let sortedItems = config.allItems.sorted { $0.itemIndex < $1.itemIndex }
        
        for item in sortedItems {
            let itemData = LotteryConfigItemData(
                index: Int(item.itemIndex),
                prizeName: item.prizeName ?? "",
                toolType: toolType
            )
            items.append(itemData)
        }
        
        // 如果项目数量不足，补充空项目
        while items.count < itemCount {
            let item = LotteryConfigItemData(
                index: items.count,
                prizeName: "",
                toolType: toolType
            )
            items.append(item)
        }
    }
    
    // MARK: - 验证
    
    /**
     * 验证表单数据
     */
    private func validate() {
        validationErrors.removeAll()
        
        // 验证积分消耗
        if costPerPlay < 0 {
            validationErrors.append("每次消耗积分不能为负数")
        }
        
        // 验证奖品名称
        let emptyCount = items.filter { $0.prizeName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }.count
        let nonEmptyNames = items.compactMap { item in
            let trimmed = item.prizeName.trimmingCharacters(in: .whitespacesAndNewlines)
            return trimmed.isEmpty ? nil : trimmed
        }
        let duplicateNames = Set(nonEmptyNames).count != nonEmptyNames.count
        
        // 添加验证错误
        if emptyCount > 0 {
            validationErrors.append("还有 \(emptyCount) 个\(localizedItemTitlePrefix)的奖品名称未填写")
        }
        
        if duplicateNames {
            validationErrors.append("存在重复的奖品名称，请检查并修改")
        }
    }
    
    // MARK: - 数据导出
    
    /**
     * 导出为配置数据
     */
    func toConfigData() -> (toolType: String, itemCount: Int, costPerPlay: Int, prizeNames: [String]) {
        let prizeNames = items.map { $0.prizeName.trimmingCharacters(in: .whitespacesAndNewlines) }
        return (
            toolType: toolType.rawValue,
            itemCount: itemCount,
            costPerPlay: costPerPlay,
            prizeNames: prizeNames
        )
    }
    
    /**
     * 重置表单
     */
    func reset() {
        toolType = .wheel
        itemCount = 8
        costPerPlay = 10
        validationErrors.removeAll()
        isSaving = false
        setupDefaultItems()
    }
}

/**
 * 抽奖配置项目数据
 */
class LotteryConfigItemData: ObservableObject, Identifiable {
    let id = UUID()
    
    /// 项目索引
    @Published var index: Int
    
    /// 奖品名称
    @Published var prizeName: String
    
    /// 道具类型
    let toolType: LotteryConfig.ToolType
    
    /// 显示标题
    var displayTitle: String {
        switch toolType {
        case .wheel:
            return "分区 \(index + 1)"
        case .blindbox:
            return "盲盒 \(index + 1)"
        case .scratchcard:
            return "刮刮卡 \(index + 1)"
        }
    }
    
    /// 占位符文本
    var placeholderText: String {
        switch toolType {
        case .wheel:
            return "输入分区奖品..."
        case .blindbox:
            return "输入盲盒奖品..."
        case .scratchcard:
            return "输入刮刮卡奖品..."
        }
    }
    
    init(index: Int, prizeName: String, toolType: LotteryConfig.ToolType) {
        self.index = index
        self.prizeName = prizeName
        self.toolType = toolType
    }
}
