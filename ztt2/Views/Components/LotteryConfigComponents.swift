//
//  LotteryConfigComponents.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 区域标题组件
 */
struct SectionHeader: View {
    let title: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)
            
            Text(title)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
        }
    }
}

/**
 * 计数器按钮样式
 */
struct CounterButtonStyle: ButtonStyle {
    let color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 22, weight: .semibold))
            .foregroundColor(color)
            .frame(width: 36, height: 36)
            .background(color.opacity(0.1))
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

/**
 * 抽奖配置项目卡片
 */
struct LotteryConfigItemCard: View {
    
    @ObservedObject var item: LotteryConfigItemData
    let toolColor: Color
    let onPrizeNameChanged: (String) -> Void
    
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 项目标题
            HStack {
                Text(item.displayTitle)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 状态指示器
                Circle()
                    .fill(item.prizeName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 
                          Color(.systemGray4) : toolColor)
                    .frame(width: 8, height: 8)
            }
            
            // 奖品名称输入
            HStack(spacing: 12) {
                TextField(item.placeholderText, text: Binding(
                    get: { item.prizeName },
                    set: { newValue in
                        item.prizeName = newValue
                        onPrizeNameChanged(newValue)
                    }
                ))
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .focused($isTextFieldFocused)
                .onSubmit {
                    isTextFieldFocused = false
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isTextFieldFocused ? toolColor : toolColor.opacity(0.2), lineWidth: isTextFieldFocused ? 1.5 : 1)
        )
        .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 2)
    }
}

/**
 * 大转盘配置视图
 */
struct WheelConfigView: View {
    let selectedMember: Member
    let existingConfig: LotteryConfig?
    let onConfigurationComplete: () -> Void
    let onDismiss: () -> Void
    
    var body: some View {
        LotteryConfigFormView(
            selectedMember: selectedMember,
            toolType: .wheel,
            existingConfig: existingConfig,
            onConfigurationComplete: onConfigurationComplete,
            onDismiss: onDismiss
        )
    }
}

/**
 * 盲盒配置视图
 */
struct BlindBoxConfigView: View {
    let selectedMember: Member
    let existingConfig: LotteryConfig?
    let onConfigurationComplete: () -> Void
    let onDismiss: () -> Void
    
    var body: some View {
        LotteryConfigFormView(
            selectedMember: selectedMember,
            toolType: .blindbox,
            existingConfig: existingConfig,
            onConfigurationComplete: onConfigurationComplete,
            onDismiss: onDismiss
        )
    }
}

/**
 * 刮刮卡配置视图
 */
struct ScratchCardConfigView: View {
    let selectedMember: Member
    let existingConfig: LotteryConfig?
    let onConfigurationComplete: () -> Void
    let onDismiss: () -> Void
    
    var body: some View {
        LotteryConfigFormView(
            selectedMember: selectedMember,
            toolType: .scratchcard,
            existingConfig: existingConfig,
            onConfigurationComplete: onConfigurationComplete,
            onDismiss: onDismiss
        )
    }
}

// MARK: - Extensions

extension LotteryConfigFormData {
    /**
     * 从现有配置加载数据
     */
    func loadFromExistingConfig(_ config: LotteryConfig) {
        self.toolType = config.lotteryToolType
        self.itemCount = Int(config.itemCount)
        self.costPerPlay = Int(config.costPerPlay)
        
        // 加载现有项目数据
        items.removeAll()
        
        let sortedItems = config.allItems.sorted { $0.itemIndex < $1.itemIndex }
        
        for item in sortedItems {
            let itemData = LotteryConfigItemData(
                index: Int(item.itemIndex),
                prizeName: item.prizeName ?? "",
                toolType: toolType
            )
            items.append(itemData)
        }
        
        // 如果项目数量不足，补充空项目
        while items.count < itemCount {
            let item = LotteryConfigItemData(
                index: items.count,
                prizeName: "",
                toolType: toolType
            )
            items.append(item)
        }
    }
}

// MARK: - Previews

#Preview("大转盘配置") {
    WheelConfigView(
        selectedMember: Member(),
        existingConfig: nil,
        onConfigurationComplete: {},
        onDismiss: {}
    )
    .environmentObject(DataManager.shared)
}

#Preview("盲盒配置") {
    BlindBoxConfigView(
        selectedMember: Member(),
        existingConfig: nil,
        onConfigurationComplete: {},
        onDismiss: {}
    )
    .environmentObject(DataManager.shared)
}

#Preview("刮刮卡配置") {
    ScratchCardConfigView(
        selectedMember: Member(),
        existingConfig: nil,
        onConfigurationComplete: {},
        onDismiss: {}
    )
    .environmentObject(DataManager.shared)
}
