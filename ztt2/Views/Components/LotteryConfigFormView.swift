//
//  LotteryConfigFormView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖配置表单视图
 * 通用的抽奖道具配置界面，支持大转盘、盲盒、刮刮卡
 */
struct LotteryConfigFormView: View {
    
    // MARK: - Environment
    
    @EnvironmentObject private var dataManager: DataManager
    
    // MARK: - Properties
    
    /// 选择的成员
    let selectedMember: Member
    
    /// 道具类型
    let toolType: LotteryConfig.ToolType
    
    /// 现有配置（用于编辑）
    let existingConfig: LotteryConfig?
    
    /// 完成配置的回调
    let onConfigurationComplete: () -> Void
    
    /// 关闭弹窗的回调
    let onDismiss: () -> Void
    
    // MARK: - State
    
    @StateObject private var formData = LotteryConfigFormData()
    @State private var showErrorAlert = false
    @State private var showSuccessAlert = false
    @State private var alertMessage = ""
    @State private var isEditing = false
    @State private var animationTrigger = false
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        formData.isValid
    }
    
    private var toolColor: Color {
        switch toolType {
        case .wheel:
            return Color(hex: "#FF9500")
        case .blindbox:
            return Color(hex: "#007AFF")
        case .scratchcard:
            return Color(hex: "#FF2D92")
        }
    }
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissKeyboard()
                }
            
            // 表单对话框
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 标题栏
                    headerSection
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)
                    
                    // 表单内容区域
                    formContentSection
                    
                    // 底部按钮区域
                    bottomButtonSection
                }
                .frame(maxWidth: min(geometry.size.width - 40, 420))
                .frame(maxHeight: geometry.size.height * 0.8)
                .background(Color.white)
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(toolColor.opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
            }
        }
        .onAppear {
            setupFormData()
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                animationTrigger = true
            }
        }
        .alert("错误", isPresented: $showErrorAlert) {
            Button("确定") {}
        } message: {
            Text(alertMessage)
        }
        .alert("成功", isPresented: $showSuccessAlert) {
            Button("确定") {
                onConfigurationComplete()
            }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("\(toolType.displayName)配置")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("为 \(selectedMember.name ?? "未知成员") 配置")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onDismiss()
                }
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
        .background(Color.white)
    }
    
    // MARK: - Form Content Section
    
    private var formContentSection: some View {
        ScrollView(.vertical, showsIndicators: true) {
            VStack(spacing: 24) {
                // 成员信息卡片
                memberInfoCard
                
                // 基本配置
                basicConfigSection
                
                // 道具项目配置
                itemConfigSection
                
                // 验证错误显示
                if !formData.validationErrors.isEmpty {
                    validationErrorsSection
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
            .contentShape(Rectangle())
            .onTapGesture {
                dismissKeyboard()
            }
        }
        .frame(maxHeight: UIScreen.main.bounds.height * 0.5)
    }
    
    // MARK: - Member Info Card
    
    private var memberInfoCard: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                // 成员头像
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    toolColor.opacity(0.8),
                                    toolColor
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                    
                    Text(String(selectedMember.name?.prefix(1) ?? "?"))
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(selectedMember.name ?? "未知成员")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("当前积分: \(selectedMember.currentPoints)")
                        .font(.system(size: 14))
                        .foregroundColor(toolColor)
                }
                
                Spacer()
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(toolColor.opacity(0.2), lineWidth: 1)
        )
    }

    // MARK: - Basic Config Section

    private var basicConfigSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "基本配置", icon: "gearshape.fill", color: toolColor)

            VStack(spacing: 16) {
                // 道具数量配置
                itemCountConfig

                // 积分消耗配置
                costPerPlayConfig
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray5), lineWidth: 1)
            )
        }
    }

    private var itemCountConfig: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("\(formData.localizedItemTitlePrefix)数量")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                Text("\(formData.itemCount)")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(toolColor)
            }

            HStack(spacing: 16) {
                Button("-") {
                    formData.updateItemCount(formData.itemCount - 1)
                }
                .buttonStyle(CounterButtonStyle(color: toolColor))
                .disabled(formData.itemCount <= formData.minItemCount)

                Slider(
                    value: Binding(
                        get: { Double(formData.itemCount) },
                        set: { formData.updateItemCount(Int($0)) }
                    ),
                    in: Double(formData.minItemCount)...Double(formData.maxItemCount),
                    step: 1
                )
                .accentColor(toolColor)

                Button("+") {
                    formData.updateItemCount(formData.itemCount + 1)
                }
                .buttonStyle(CounterButtonStyle(color: toolColor))
                .disabled(formData.itemCount >= formData.maxItemCount)
            }

            Text("范围: \(formData.minItemCount) - \(formData.maxItemCount)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }

    private var costPerPlayConfig: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("每次消耗积分")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            TextField("输入积分数量", value: $formData.costPerPlay, format: .number)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .keyboardType(.numberPad)
        }
    }

    // MARK: - Item Config Section

    private var itemConfigSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "\(formData.localizedItemTitlePrefix)配置", icon: "list.bullet", color: toolColor)

            LazyVStack(spacing: 12) {
                ForEach(Array(formData.items.enumerated()), id: \.element.id) { index, item in
                    LotteryConfigItemCard(
                        item: item,
                        toolColor: toolColor,
                        onPrizeNameChanged: { newName in
                            formData.updateItemPrizeName(at: index, name: newName)
                        }
                    )
                }
            }
        }
    }

    // MARK: - Validation Errors Section

    private var validationErrorsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(formData.validationErrors, id: \.self) { error in
                HStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.errorColor)

                    Text(error)
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.errorColor)

                    Spacer()
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(DesignSystem.Colors.errorColor.opacity(0.1))
        .cornerRadius(8)
    }

    // MARK: - Bottom Button Section

    private var bottomButtonSection: some View {
        HStack(spacing: 16) {
            Button("取消") {
                onDismiss()
            }
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(DesignSystem.Colors.textSecondary)
            .frame(maxWidth: .infinity)
            .frame(height: 48)
            .background(Color(.systemGray6))
            .cornerRadius(12)

            Button(isEditing ? "更新配置" : "保存配置") {
                saveConfiguration()
            }
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 48)
            .background(isFormValid ? toolColor : Color(.systemGray4))
            .cornerRadius(12)
            .disabled(!isFormValid || formData.isSaving)
            .overlay(
                Group {
                    if formData.isSaving {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                }
            )
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
        .background(Color.white)
    }

    // MARK: - Helper Methods

    private func setupFormData() {
        formData.setToolType(toolType)

        if let config = existingConfig {
            formData.loadFromExistingConfig(config)
            isEditing = true
        }
    }

    private func saveConfiguration() {
        guard isFormValid else { return }

        formData.isSaving = true

        let configData = formData.toConfigData()

        if let existingConfig = existingConfig {
            // 更新现有配置
            dataManager.updateLotteryConfig(
                existingConfig,
                itemCount: Int32(configData.itemCount),
                costPerPlay: Int32(configData.costPerPlay)
            )

            // 重新创建道具项目
            dataManager.updateLotteryItems(
                for: existingConfig,
                prizeNames: configData.prizeNames
            )
        } else {
            // 创建新配置
            let newConfig = dataManager.createLotteryConfig(
                for: selectedMember,
                toolType: configData.toolType,
                itemCount: Int32(configData.itemCount),
                costPerPlay: Int32(configData.costPerPlay)
            )

            // 创建道具项目
            dataManager.createLotteryItems(
                for: newConfig,
                prizeNames: configData.prizeNames
            )
        }

        formData.isSaving = false
        alertMessage = isEditing ? "配置更新成功！" : "配置保存成功！"
        showSuccessAlert = true
    }

    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}
